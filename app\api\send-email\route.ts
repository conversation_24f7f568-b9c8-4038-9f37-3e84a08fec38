import { type NextRequest, NextResponse } from "next/server"
import { emailService } from "@/lib/email-service"

interface EmailData {
  clientEmail: string
  clientName: string
  clientId: string
  documentContent: string
}

export async function POST(request: NextRequest) {
  try {
    const { clientEmail, clientName, clientId, documentContent }: EmailData = await request.json()

    // Generate the client portal link
    const portalLink = `${process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000"}/client-portal/${clientId}`

    // Send email to client
    const clientEmailSent = await emailService.sendClientNotification({
      clientEmail,
      clientName,
      clientId,
      portalLink,
      documentContent,
    })

    if (!clientEmailSent) {
      throw new Error("Échec de l'envoi de l'email au client")
    }

    // Send notification to agent (optional)
    const agentEmail = process.env.AGENT_EMAIL || "<EMAIL>"
    await emailService.sendAgentNotification({
      agentEmail,
      clientName,
      clientEmail,
      clientId,
    })

    return NextResponse.json({
      success: true,
      message: "Email envoyé avec succès",
      portalLink,
      emailSent: true,
    })
  } catch (error) {
    console.error("[v0] Error sending email:", error)
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "Erreur lors de l'envoi de l'email",
        emailSent: false,
      },
      { status: 500 },
    )
  }
}
