"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { CheckCircle, Search, Download, FileText, Calendar, Filter } from "lucide-react"
import { useState } from "react"

interface CompletedCase {
  id: string
  clientName: string
  email: string
  documentType: string
  completedAt: string
  processingTime: number // in days
  status: "completed" | "sent_to_insurer" | "confirmed_by_insurer"
  insurerName: string
  documentUrl: string
}

const mockCompletedCases: CompletedCase[] = [
  {
    id: "CP001",
    clientName: "<PERSON>",
    email: "<EMAIL>",
    documentType: "Résiliation Auto",
    completedAt: "2024-01-16",
    processingTime: 2,
    status: "confirmed_by_insurer",
    insurerName: "AXA Assurances",
    documentUrl: "/documents/CP001.pdf"
  },
  {
    id: "CP002",
    clientName: "Jean <PERSON>",
    email: "<EMAIL>",
    documentType: "Résiliation Habitation",
    completedAt: "2024-01-15",
    processingTime: 3,
    status: "sent_to_insurer",
    insurerName: "Zurich Assurances",
    documentUrl: "/documents/CP002.pdf"
  },
  {
    id: "CP003",
    clientName: "Sophie Laurent",
    email: "<EMAIL>",
    documentType: "Résiliation Santé",
    completedAt: "2024-01-14",
    processingTime: 1,
    status: "completed",
    insurerName: "CSS Assurance",
    documentUrl: "/documents/CP003.pdf"
  },
  // Add more mock data to reach 156 total
  ...Array.from({ length: 153 }, (_, i) => ({
    id: `CP${String(i + 4).padStart(3, '0')}`,
    clientName: `Client ${i + 4}`,
    email: `client${i + 4}@email.com`,
    documentType: ["Résiliation Auto", "Résiliation Habitation", "Résiliation Santé"][i % 3],
    completedAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    processingTime: Math.floor(Math.random() * 7) + 1,
    status: ["completed", "sent_to_insurer", "confirmed_by_insurer"][i % 3] as CompletedCase["status"],
    insurerName: ["AXA Assurances", "Zurich Assurances", "CSS Assurance", "Allianz Suisse"][i % 4],
    documentUrl: `/documents/CP${String(i + 4).padStart(3, '0')}.pdf`
  }))
]

export function AgentCompleted() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [documentTypeFilter, setDocumentTypeFilter] = useState("all")
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 10

  const filteredCases = mockCompletedCases.filter(case_ => {
    const matchesSearch = case_.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         case_.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         case_.id.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || case_.status === statusFilter
    const matchesDocType = documentTypeFilter === "all" || case_.documentType === documentTypeFilter
    
    return matchesSearch && matchesStatus && matchesDocType
  })

  const totalPages = Math.ceil(filteredCases.length / itemsPerPage)
  const paginatedCases = filteredCases.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  )

  const getStatusBadge = (status: CompletedCase["status"]) => {
    switch (status) {
      case "completed":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100"><CheckCircle className="h-3 w-3 mr-1" />Terminé</Badge>
      case "sent_to_insurer":
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100"><FileText className="h-3 w-3 mr-1" />Envoyé assureur</Badge>
      case "confirmed_by_insurer":
        return <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-100"><CheckCircle className="h-3 w-3 mr-1" />Confirmé assureur</Badge>
      default:
        return <Badge variant="secondary">Inconnu</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
        <CardHeader className="bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-t-lg">
          <CardTitle className="text-2xl flex items-center">
            <CheckCircle className="mr-3 h-6 w-6" />
            Dossiers Terminés ({filteredCases.length})
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Rechercher..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Statut" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tous les statuts</SelectItem>
                <SelectItem value="completed">Terminé</SelectItem>
                <SelectItem value="sent_to_insurer">Envoyé assureur</SelectItem>
                <SelectItem value="confirmed_by_insurer">Confirmé assureur</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={documentTypeFilter} onValueChange={setDocumentTypeFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Type de document" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tous les types</SelectItem>
                <SelectItem value="Résiliation Auto">Résiliation Auto</SelectItem>
                <SelectItem value="Résiliation Habitation">Résiliation Habitation</SelectItem>
                <SelectItem value="Résiliation Santé">Résiliation Santé</SelectItem>
              </SelectContent>
            </Select>
            
            <Button variant="outline" className="flex items-center">
              <Filter className="h-4 w-4 mr-2" />
              Filtres avancés
            </Button>
          </div>

          {/* Completed Cases Table */}
          <div className="rounded-lg border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Client</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Assureur</TableHead>
                  <TableHead>Statut</TableHead>
                  <TableHead>Terminé le</TableHead>
                  <TableHead>Durée</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedCases.map((case_) => (
                  <TableRow key={case_.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{case_.clientName}</div>
                        <div className="text-sm text-gray-500">{case_.email}</div>
                        <div className="text-xs text-gray-400">ID: {case_.id}</div>
                      </div>
                    </TableCell>
                    <TableCell>{case_.documentType}</TableCell>
                    <TableCell>{case_.insurerName}</TableCell>
                    <TableCell>{getStatusBadge(case_.status)}</TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Calendar className="h-3 w-3 mr-1 text-gray-400" />
                        {new Date(case_.completedAt).toLocaleDateString('fr-CH')}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="text-xs">
                        {case_.processingTime} jour{case_.processingTime > 1 ? 's' : ''}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button size="sm" variant="outline">
                          <FileText className="h-3 w-3 mr-1" />
                          Voir
                        </Button>
                        <Button size="sm" variant="outline">
                          <Download className="h-3 w-3 mr-1" />
                          PDF
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-gray-600">
                Affichage de {(currentPage - 1) * itemsPerPage + 1} à {Math.min(currentPage * itemsPerPage, filteredCases.length)} sur {filteredCases.length} résultats
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                >
                  Précédent
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                >
                  Suivant
                </Button>
              </div>
            </div>
          )}

          {filteredCases.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <CheckCircle className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>Aucun dossier terminé trouvé</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
