import type { NextRequest } from "next/server"
import { generateClientEmailTemplate } from "@/lib/email-templates"

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const clientName = searchParams.get("clientName") || "<PERSON>"
  const clientId = searchParams.get("clientId") || "CLI_PREVIEW"

  const portalLink = `${process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000"}/client-portal/${clientId}`

  const template = generateClientEmailTemplate({
    clientName,
    portalLink,
    documentContent: "Document de résiliation généré automatiquement...",
  })

  return new Response(template.html, {
    headers: {
      "Content-Type": "text/html; charset=utf-8",
    },
  })
}
