"use client"

import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Download, Eye, EyeOff } from "lucide-react"
import { useState } from "react"

interface DocumentViewerProps {
  content: string
  clientId: string
}

export function DocumentViewer({ content, clientId }: DocumentViewerProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  const downloadDocument = () => {
    const blob = new Blob([content], { type: "text/plain;charset=utf-8" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `resiliation_${clientId}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const previewContent = content.substring(0, 500) + (content.length > 500 ? "..." : "")

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <p className="text-sm text-gray-600">Vérifiez que toutes les informations sont correctes avant de continuer.</p>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={() => setIsExpanded(!isExpanded)}>
            {isExpanded ? (
              <>
                <EyeOff className="h-4 w-4 mr-2" />
                Réduire
              </>
            ) : (
              <>
                <Eye className="h-4 w-4 mr-2" />
                Voir tout
              </>
            )}
          </Button>
          <Button variant="outline" size="sm" onClick={downloadDocument}>
            <Download className="h-4 w-4 mr-2" />
            Télécharger
          </Button>
        </div>
      </div>

      <Card>
        <CardContent className="p-6">
          <div
            className={`bg-gray-50 border rounded-lg p-4 font-mono text-sm whitespace-pre-line ${
              isExpanded ? "max-h-none" : "max-h-64"
            } overflow-y-auto`}
          >
            {isExpanded ? content : previewContent}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
