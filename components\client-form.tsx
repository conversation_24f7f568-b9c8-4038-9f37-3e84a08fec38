"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Plus, Trash2, FileText, Mail, Eye } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { DocumentPreview } from "./document-preview"

interface PersonInfo {
  nom: string
  prenom: string
  dateNaissance: string
  numeroPolice: string
}

interface ClientData {
  nomPrenom: string
  adresse: string
  npaVille: string
  destinataire: string
  lieuDate: string
  personnes: PersonInfo[]
  dateLamal: string
  dateLCA: string
  email: string
}

export function ClientForm() {
  const { toast } = useToast()
  const [clientData, setClientData] = useState<ClientData>({
    nomPrenom: "",
    adresse: "",
    npaVille: "",
    destinataire: "",
    lieuDate: "",
    personnes: [{ nom: "", prenom: "", dateNaissance: "", numeroPolice: "" }],
    dateLamal: "",
    dateLCA: "",
    email: "",
  })

  const [isLoading, setIsLoading] = useState(false)
  const [generatedDocument, setGeneratedDocument] = useState<string | null>(null)
  const [clientId, setClientId] = useState<string | null>(null)
  const [showPreview, setShowPreview] = useState(false)

  const addPerson = () => {
    if (clientData.personnes.length < 4) {
      setClientData({
        ...clientData,
        personnes: [...clientData.personnes, { nom: "", prenom: "", dateNaissance: "", numeroPolice: "" }],
      })
    }
  }

  const removePerson = (index: number) => {
    if (clientData.personnes.length > 1) {
      const newPersonnes = clientData.personnes.filter((_, i) => i !== index)
      setClientData({ ...clientData, personnes: newPersonnes })
    }
  }

  const updatePerson = (index: number, field: keyof PersonInfo, value: string) => {
    const newPersonnes = [...clientData.personnes]
    newPersonnes[index] = { ...newPersonnes[index], [field]: value }
    setClientData({ ...clientData, personnes: newPersonnes })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const generateResponse = await fetch("/api/generate-document", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(clientData),
      })

      const generateResult = await generateResponse.json()

      if (!generateResult.success) {
        throw new Error(generateResult.message)
      }

      setGeneratedDocument(generateResult.documentContent)
      setClientId(generateResult.clientId)

      const emailResponse = await fetch("/api/send-email", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          clientEmail: clientData.email,
          clientName: clientData.nomPrenom,
          clientId: generateResult.clientId,
          documentContent: generateResult.documentContent,
        }),
      })

      const emailResult = await emailResponse.json()

      if (!emailResult.success) {
        throw new Error(emailResult.message)
      }

      toast({
        title: "Dossier créé avec succès",
        description: `Document généré et email envoyé à ${clientData.email}. Lien portail: ${emailResult.portalLink}`,
      })

      // Show preview
      setShowPreview(true)
    } catch (error) {
      console.error("[v0] Error:", error)
      toast({
        title: "Erreur",
        description: error instanceof Error ? error.message : "Une erreur est survenue lors de la création du dossier.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const resetForm = () => {
    setClientData({
      nomPrenom: "",
      adresse: "",
      npaVille: "",
      destinataire: "",
      lieuDate: "",
      personnes: [{ nom: "", prenom: "", dateNaissance: "", numeroPolice: "" }],
      dateLamal: "",
      dateLCA: "",
      email: "",
    })
    setGeneratedDocument(null)
    setClientId(null)
    setShowPreview(false)
  }

  if (showPreview && generatedDocument) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-semibold text-gray-900">Document Généré</h3>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setShowPreview(false)}>
              <Eye className="h-4 w-4 mr-2" />
              Retour au Formulaire
            </Button>
            <Button variant="outline" onClick={resetForm}>
              Nouveau Dossier
            </Button>
          </div>
        </div>

        <DocumentPreview content={generatedDocument} clientId={clientId || ""} />
      </div>
    )
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      {/* Informations du demandeur */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-700">
            <FileText className="h-5 w-5" />
            Informations du Demandeur
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="nomPrenom">Nom et Prénom *</Label>
              <Input
                id="nomPrenom"
                value={clientData.nomPrenom}
                onChange={(e) => setClientData({ ...clientData, nomPrenom: e.target.value })}
                placeholder="Jean Dupont"
                required
              />
            </div>
            <div>
              <Label htmlFor="email">Email du Client *</Label>
              <Input
                id="email"
                type="email"
                value={clientData.email}
                onChange={(e) => setClientData({ ...clientData, email: e.target.value })}
                placeholder="<EMAIL>"
                required
              />
            </div>
          </div>

          <div>
            <Label htmlFor="adresse">Adresse *</Label>
            <Textarea
              id="adresse"
              value={clientData.adresse}
              onChange={(e) => setClientData({ ...clientData, adresse: e.target.value })}
              placeholder="Rue de la Paix 123"
              required
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="npaVille">NPA Ville *</Label>
              <Input
                id="npaVille"
                value={clientData.npaVille}
                onChange={(e) => setClientData({ ...clientData, npaVille: e.target.value })}
                placeholder="1000 Lausanne"
                required
              />
            </div>
            <div>
              <Label htmlFor="lieuDate">Lieu et Date *</Label>
              <Input
                id="lieuDate"
                value={clientData.lieuDate}
                onChange={(e) => setClientData({ ...clientData, lieuDate: e.target.value })}
                placeholder="Lausanne, le 19 septembre 2025"
                required
              />
            </div>
          </div>

          <div>
            <Label htmlFor="destinataire">Destinataire (Compagnie d'assurance) *</Label>
            <Input
              id="destinataire"
              value={clientData.destinataire}
              onChange={(e) => setClientData({ ...clientData, destinataire: e.target.value })}
              placeholder="Assura SA, Service Résiliation"
              required
            />
          </div>
        </CardContent>
      </Card>

      {/* Personnes assurées */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between text-blue-700">
            <span className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Personnes Assurées
            </span>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addPerson}
              disabled={clientData.personnes.length >= 4}
              className="text-blue-600 border-blue-200 hover:bg-blue-50"
            >
              <Plus className="h-4 w-4 mr-1" />
              Ajouter
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {clientData.personnes.map((personne, index) => (
            <div key={index} className="p-4 border rounded-lg bg-gray-50">
              <div className="flex items-center justify-between mb-4">
                <h4 className="font-medium text-gray-900">Personne {index + 1}</h4>
                {clientData.personnes.length > 1 && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removePerson(index)}
                    className="text-red-600 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor={`nom-${index}`}>Nom *</Label>
                  <Input
                    id={`nom-${index}`}
                    value={personne.nom}
                    onChange={(e) => updatePerson(index, "nom", e.target.value)}
                    placeholder="Dupont"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor={`prenom-${index}`}>Prénom *</Label>
                  <Input
                    id={`prenom-${index}`}
                    value={personne.prenom}
                    onChange={(e) => updatePerson(index, "prenom", e.target.value)}
                    placeholder="Jean"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor={`dateNaissance-${index}`}>Date de Naissance *</Label>
                  <Input
                    id={`dateNaissance-${index}`}
                    type="date"
                    value={personne.dateNaissance}
                    onChange={(e) => updatePerson(index, "dateNaissance", e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor={`numeroPolice-${index}`}>Numéro de Police *</Label>
                  <Input
                    id={`numeroPolice-${index}`}
                    value={personne.numeroPolice}
                    onChange={(e) => updatePerson(index, "numeroPolice", e.target.value)}
                    placeholder="123456789"
                    required
                  />
                </div>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Dates de résiliation */}
      <Card>
        <CardHeader>
          <CardTitle className="text-blue-700">Dates de Résiliation</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="dateLamal">Date de résiliation LAMal *</Label>
              <Input
                id="dateLamal"
                type="date"
                value={clientData.dateLamal}
                onChange={(e) => setClientData({ ...clientData, dateLamal: e.target.value })}
                required
              />
            </div>
            <div>
              <Label htmlFor="dateLCA">Date de résiliation LCA complémentaires *</Label>
              <Input
                id="dateLCA"
                type="date"
                value={clientData.dateLCA}
                onChange={(e) => setClientData({ ...clientData, dateLCA: e.target.value })}
                required
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <Separator />

      <div className="flex justify-center">
        <Button
          type="submit"
          size="lg"
          disabled={isLoading}
          className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-8 py-3"
        >
          {isLoading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Traitement en cours...
            </>
          ) : (
            <>
              <Mail className="h-4 w-4 mr-2" />
              Générer le Document et Envoyer l'Email
            </>
          )}
        </Button>
      </div>
    </form>
  )
}
